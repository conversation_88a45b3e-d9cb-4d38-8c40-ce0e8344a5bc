'use client';

import { CustomImage } from '@/components/atoms/CustomImage/CustomImage';
import { cn } from '@/utils/cn';
import { sanitizeHtml } from '@/utils/sanitizeHtml';
import React, { useState, useCallback, useEffect } from 'react';
import Icon from '@/components/atoms/Icon';
import { CreativeWritingRenderer } from '@/components/molecules/QuestionRenderer/CreativeWritingRenderer';
import { FillBlankContentProcessor } from '@/components/molecules/QuestionRenderer/FillBlankContentProcessor';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { GripVertical, Trash2, ChevronUp, ChevronDown, Edit } from 'lucide-react';
import { Button } from '@/components/atoms/Button/Button';
import { IWorksheetQuestion } from '@/apis/worksheetQuestionApi';
import { handleDeleteQuestionAction, handleReorderQuestionsAction, handleUpdateQuestionAction } from '@/actions/worksheetQuestion.action';
import { DeleteQuestionModal } from './DeleteQuestionModal';
import { QuestionFormModal } from '../../organisms/WorksheetQuestionManager/QuestionFormModal/QuestionFormModal';

// Define the type for a single question (keeping backward compatibility)
export type Question = {
  type:
    | 'multiple_choice'
    | 'single_choice'
    | 'fill_blank'
    | 'creative_writing'
    | string;
  content: string;
  image?: string | null;
  svgCode?: string;
  imagePrompt?: string | null;
  options: string[];
  answer: string[];
  explain: string;
  prompt?: string; // For creative writing prompts
  subject?: string; // Subject of the question from API response
};

// Enhanced props type with drag-and-drop and delete functionality
export type QuestionListingViewProps = {
  questions?: Question[];
  containerClass?: string;
  isHtmlContent?: boolean;
  worksheetInfo?: {
    topic?: string;
    subject?: string; // Added subject
    grade?: string;
    language?: string;
    level?: string;
    totalQuestions?: number;
  };
  // New props for enhanced functionality
  worksheetId?: string;
  enableDragAndDrop?: boolean;
  enableDelete?: boolean;
  enableArrowButtons?: boolean;
  userRole?: string;
  onQuestionsChange?: (questions: Question[]) => void;
  isReadOnly?: boolean;
};

const QuestionListingView: React.FC<QuestionListingViewProps> = ({
  questions = [],
  containerClass = '',
  isHtmlContent = false,
  worksheetInfo,
  worksheetId,
  enableDragAndDrop = false,
  enableDelete = false,
  enableArrowButtons = false,
  userRole,
  onQuestionsChange,
  isReadOnly = false,
}) => {
  const [localQuestions, setLocalQuestions] = useState<Question[]>(questions);
  const [isLoading, setIsLoading] = useState(false);
  const [deletingQuestionIndex, setDeletingQuestionIndex] = useState<number | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [questionToDelete, setQuestionToDelete] = useState<number | null>(null);
  const [editingQuestion, setEditingQuestion] = useState<Question | null>(null);
  const [editingQuestionIndex, setEditingQuestionIndex] = useState<number | null>(null);
  const [showEditModal, setShowEditModal] = useState(false);


  // Update local questions when props change
  React.useEffect(() => {
    setLocalQuestions(questions);
  }, [questions]);

  // Determine if action buttons should be visible (always show for authorized users)
  const canShowActionButtons = !isReadOnly && userRole && ['admin', 'school_manager', 'teacher', 'independent_teacher'].includes(userRole);
  const canShowDeleteButton = canShowActionButtons && enableDelete;

  // Handle drag end for reordering
  const handleDragEnd = useCallback(async (result: DropResult) => {
    if (!result.destination || !worksheetId) return;

    const items = Array.from(localQuestions);
    const [reorderedItem] = items.splice(result.source.index, 1);
    items.splice(result.destination.index, 0, reorderedItem);

    // Optimistic update
    setLocalQuestions(items);
    onQuestionsChange?.(items);

    // Update positions for API call - using the correct format for the API
    const reorderData = items.map((question, index) => ({
      id: (question as any).id || `temp-${index}`,
      position: index + 1
    }));

    try {
      setIsLoading(true);
      // Use the bulk reorder format that the API expects
      const response = await handleReorderQuestionsAction(worksheetId, { questions: reorderData });

      if (response.status !== 'success') {
        // Rollback on error
        setLocalQuestions(questions);
        onQuestionsChange?.(questions);
        console.error('Failed to reorder questions:', response.message);
      }
    } catch (error) {
      // Rollback on error
      setLocalQuestions(questions);
      onQuestionsChange?.(questions);
      console.error('Error reordering questions:', error);
    } finally {
      setIsLoading(false);
    }
  }, [localQuestions, worksheetId, questions, onQuestionsChange]);

  // Handle question deletion
  const handleDeleteQuestion = useCallback((index: number) => {
    if (!worksheetId || isReadOnly) return;
    setQuestionToDelete(index);
    setShowDeleteModal(true);
  }, [worksheetId, isReadOnly]);

  const confirmDeleteQuestion = useCallback(async () => {
    if (questionToDelete === null || !worksheetId) return;

    const questionToDeleteData = localQuestions[questionToDelete];
    const questionId = (questionToDeleteData as any).id;

    if (!questionId) return;

    setDeletingQuestionIndex(questionToDelete);

    try {
      const response = await handleDeleteQuestionAction(worksheetId, questionId);

      if (response.status === 'success') {
        const updatedQuestions = localQuestions.filter((_, i) => i !== questionToDelete);
        setLocalQuestions(updatedQuestions);
        onQuestionsChange?.(updatedQuestions);
        setShowDeleteModal(false);
        setQuestionToDelete(null);
      } else {
        console.error('Failed to delete question:', response.message);
      }
    } catch (error) {
      console.error('Error deleting question:', error);
    } finally {
      setDeletingQuestionIndex(null);
    }
  }, [questionToDelete, localQuestions, worksheetId, onQuestionsChange]);

  const cancelDeleteQuestion = useCallback(() => {
    setShowDeleteModal(false);
    setQuestionToDelete(null);
  }, []);

  // Handle question editing
  const handleEditQuestion = useCallback((index: number) => {
    if (!worksheetId || isReadOnly) return;
    const questionToEdit = localQuestions[index];
    setEditingQuestion(questionToEdit);
    setEditingQuestionIndex(index);
    setShowEditModal(true);
  }, [localQuestions, worksheetId, isReadOnly]);

  const handleEditSuccess = useCallback((updatedQuestion: IWorksheetQuestion) => {
    if (editingQuestionIndex === null) return;

    // Convert the updated question back to the Question format
    const convertedQuestion: Question = {
      type: updatedQuestion.type.toLowerCase().replace(/([A-Z])/g, '_$1').replace(/^_/, '') as any,
      content: updatedQuestion.content,
      options: updatedQuestion.options || [],
      answer: updatedQuestion.answer || [],
      explain: updatedQuestion.explain || '',
      image: updatedQuestion.imageUrl || null,
      imagePrompt: updatedQuestion.imagePrompt || null,
      subject: updatedQuestion.subject,
    };

    const updatedQuestions = [...localQuestions];
    updatedQuestions[editingQuestionIndex] = convertedQuestion;

    setLocalQuestions(updatedQuestions);
    onQuestionsChange?.(updatedQuestions);
    setShowEditModal(false);
    setEditingQuestion(null);
    setEditingQuestionIndex(null);
  }, [editingQuestionIndex, localQuestions, onQuestionsChange]);

  const cancelEditQuestion = useCallback(() => {
    setShowEditModal(false);
    setEditingQuestion(null);
    setEditingQuestionIndex(null);
  }, []);

  // Handle moving question up
  const handleMoveUp = useCallback(async (index: number) => {
    if (index === 0 || !worksheetId || isReadOnly) return;

    const items = Array.from(localQuestions);
    const [movedItem] = items.splice(index, 1);
    items.splice(index - 1, 0, movedItem);

    // Optimistic update
    setLocalQuestions(items);
    onQuestionsChange?.(items);

    // Update positions for API call
    const reorderData = items.map((question, idx) => ({
      id: (question as any).id || `temp-${idx}`,
      position: idx + 1
    }));

    try {
      setIsLoading(true);
      const response = await handleReorderQuestionsAction(worksheetId, { questions: reorderData });

      if (response.status !== 'success') {
        // Rollback on error
        setLocalQuestions(questions);
        onQuestionsChange?.(questions);
        console.error('Failed to reorder questions:', response.message);
      }
    } catch (error) {
      // Rollback on error
      setLocalQuestions(questions);
      onQuestionsChange?.(questions);
      console.error('Error reordering questions:', error);
    } finally {
      setIsLoading(false);
    }
  }, [localQuestions, worksheetId, questions, onQuestionsChange, isReadOnly]);

  // Handle moving question down
  const handleMoveDown = useCallback(async (index: number) => {
    if (index === localQuestions.length - 1 || !worksheetId || isReadOnly) return;

    const items = Array.from(localQuestions);
    const [movedItem] = items.splice(index, 1);
    items.splice(index + 1, 0, movedItem);

    // Optimistic update
    setLocalQuestions(items);
    onQuestionsChange?.(items);

    // Update positions for API call
    const reorderData = items.map((question, idx) => ({
      id: (question as any).id || `temp-${idx}`,
      position: idx + 1
    }));

    try {
      setIsLoading(true);
      const response = await handleReorderQuestionsAction(worksheetId, { questions: reorderData });

      if (response.status !== 'success') {
        // Rollback on error
        setLocalQuestions(questions);
        onQuestionsChange?.(questions);
        console.error('Failed to reorder questions:', response.message);
      }
    } catch (error) {
      // Rollback on error
      setLocalQuestions(questions);
      onQuestionsChange?.(questions);
      console.error('Error reordering questions:', error);
    } finally {
      setIsLoading(false);
    }
  }, [localQuestions, worksheetId, questions, onQuestionsChange, isReadOnly]);

  const questionsToRender = localQuestions;
  return (
    <div className={cn('space-y-8', containerClass)}>
      {/* Worksheet Information - Sticky and Mobile-Optimized */}
      {worksheetInfo && (
        <div className="sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm mb-4">
          {/* Mobile Layout - Minimal and Compact */}
          <div className="block md:hidden px-3 py-2">
            <div className="flex items-center justify-between text-xs">
              <div className="flex items-center gap-2">
                {worksheetInfo.subject && (
                  <span className="px-2 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
                    {worksheetInfo.subject}
                  </span>
                )}
                {worksheetInfo.grade && (
                  <span className="px-2 py-1 bg-green-100 text-green-700 rounded-full font-medium">
                    {worksheetInfo.grade}
                  </span>
                )}
              </div>
              {worksheetInfo.totalQuestions && (
                <span className="text-gray-600 font-medium">
                  {worksheetInfo.totalQuestions} Q
                </span>
              )}
            </div>
            {/* Secondary info on mobile - minimal */}
            {(worksheetInfo.topic || worksheetInfo.language || worksheetInfo.level) && (
              <div className="flex items-center gap-2 mt-1 text-xs text-gray-500 truncate">
                {worksheetInfo.topic && <span className="truncate">{worksheetInfo.topic}</span>}
                {worksheetInfo.language && <span>• {worksheetInfo.language}</span>}
                {worksheetInfo.level && <span>• {worksheetInfo.level}</span>}
              </div>
            )}
          </div>

          {/* Desktop Layout - Full Details */}
          <div className="hidden md:block p-3">
            <div className="flex flex-wrap gap-x-6 gap-y-1 text-sm">
              {worksheetInfo.subject && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">Subject:</span>
                  <span className="ml-2 text-primary">{worksheetInfo.subject}</span>
                </div>
              )}
              {worksheetInfo.topic && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">Topic:</span>
                  <span className="ml-2 text-primary">{worksheetInfo.topic}</span>
                </div>
              )}
              {worksheetInfo.grade && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">Grade:</span>
                  <span className="ml-2 text-primary">{worksheetInfo.grade}</span>
                </div>
              )}
              {worksheetInfo.level && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">Level:</span>
                  <span className="ml-2 text-primary">{worksheetInfo.level}</span>
                </div>
              )}
              {worksheetInfo.language && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">Language:</span>
                  <span className="ml-2 text-primary">
                    {worksheetInfo.language}
                  </span>
                </div>
              )}
              {worksheetInfo.totalQuestions && (
                <div className="flex items-center">
                  <span className="font-medium text-gray-600">
                    Total Questions:
                  </span>
                  <span className="ml-2 text-primary">
                    {worksheetInfo.totalQuestions}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
      {/* Questions List with optional drag-and-drop */}
      {enableDragAndDrop ? (
        <DragDropContext onDragEnd={handleDragEnd}>
          <Droppable droppableId="questions" isDropDisabled={isReadOnly || isLoading}>
            {(provided, snapshot) => (
              <div
                {...provided.droppableProps}
                ref={provided.innerRef}
                className={cn(
                  "space-y-4",
                  snapshot.isDraggingOver && "bg-base-200/50"
                )}
              >
                {questionsToRender?.map((question, index) => (
                  <Draggable
                    key={`question-${index}`}
                    draggableId={`question-${index}`}
                    index={index}
                    isDragDisabled={isReadOnly || isLoading}
                  >
                    {(provided, snapshot) => (
                      <div
                        ref={provided.innerRef}
                        {...provided.draggableProps}
                        className={cn(
                          "mb-4 md:mb-8 p-4 md:p-6 bg-white border border-gray-200 rounded-xl shadow-lg relative",
                          snapshot.isDragging && "shadow-2xl rotate-2 scale-105",
                          isLoading && "opacity-50"
                        )}

                      >


                        {/* Question Header with drag handle and action buttons */}
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 md:mb-5 pb-2 md:pb-3 border-b border-gray-200">
                          <div className="flex items-center gap-2 mb-2 sm:mb-0">
                            {/* Drag Handle */}
                            {enableDragAndDrop && !isReadOnly && (
                              <div
                                {...provided.dragHandleProps}
                                className="cursor-grab active:cursor-grabbing p-1 hover:bg-gray-100 rounded"
                              >
                                <GripVertical className="w-4 h-4 text-gray-400" />
                              </div>
                            )}

                            <span className="text-lg md:text-xl font-semibold text-gray-700">
                              Question {index + 1}
                            </span>

                            {/* Compact Reorder Buttons - Inline with Question Number */}
                            {(enableArrowButtons || enableDragAndDrop) && !isReadOnly && canShowActionButtons && (
                              <div className="join">
                                <button
                                  onClick={() => handleMoveUp(index)}
                                  disabled={index === 0 || isLoading}
                                  className="btn btn-xs btn-square btn-ghost join-item"
                                  title="Move question up"
                                >
                                  <ChevronUp className="w-3 h-3" />
                                </button>
                                <button
                                  onClick={() => handleMoveDown(index)}
                                  disabled={index === questionsToRender.length - 1 || isLoading}
                                  className="btn btn-xs btn-square btn-ghost join-item"
                                  title="Move question down"
                                >
                                  <ChevronDown className="w-3 h-3" />
                                </button>
                              </div>
                            )}
                          </div>
                          <div className="flex items-center gap-2 md:gap-3">
                            {question.subject && (
                              <span className="text-xs px-2 md:px-3 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
                                {question.subject}
                              </span>
                            )}
                            <span className="text-xs px-2 md:px-3 py-1 bg-green-100 text-green-700 rounded-full font-medium capitalize">
                              {question.type.replace('_', ' ')}
                            </span>

                            {/* Streamlined Action Bar - Edit & Delete Only */}
                            {canShowActionButtons && (
                              <div className="flex items-center gap-1">
                                {/* Edit Button */}
                                <Button
                                  variant="ghost"
                                  onClick={() => handleEditQuestion(index)}
                                  disabled={isLoading}
                                  className="p-1.5 w-8 h-8 min-w-0 hover:bg-blue-50 hover:text-blue-600 rounded-md"
                                  title="Edit question"
                                >
                                  <Edit className="w-4 h-4" />
                                </Button>

                                {/* Delete Button */}
                                {canShowDeleteButton && (
                                  <Button
                                    variant="ghost"
                                    onClick={() => handleDeleteQuestion(index)}
                                    disabled={deletingQuestionIndex === index}
                                    className="p-1.5 w-8 h-8 min-w-0 hover:bg-red-50 hover:text-red-600 rounded-md"
                                    title="Delete question"
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </Button>
                                )}
                              </div>
                            )}
                          </div>
                        </div>

            {/* Question Content */}
            <div className="bg-gray-50 p-3 md:p-5 rounded-lg mb-3 md:mb-5">
              {question.type === 'fill_blank' ? (
                <FillBlankContentProcessor
                  content={question.content}
                  answers={question.answer}
                  isHtmlContent={isHtmlContent}
                />
              ) : isHtmlContent ? (
                <div // Changed h2 to div for better semantic if content is complex HTML
                  className="text-sm md:text-base text-gray-800 leading-relaxed question-content-html" // Mobile-optimized font size
                  dangerouslySetInnerHTML={{ __html: question.content }}
                />
              ) : (
                <p className="text-sm md:text-base text-gray-800 leading-relaxed">{question.content}</p> // Mobile-optimized font size
              )}
            </div>

            {/* SVG Illustration or Image (if available) */}
            {question?.image && (
              <div
                className="my-4 w-full max-w-lg"
                dangerouslySetInnerHTML={{ __html: question.image }}
              />
            )}

            {/* Question Type Specific Rendering */}
            {/* Multiple Choice & Single Choice Options */}
            {(question.type === 'multiple_choice' ||
              question.type === 'single_choice') &&
              question?.options?.length > 0 && (
                <div className="space-y-1 mt-3 md:mt-5">
                  {question?.options?.map((option, optionIndex) => {
                    const isAnswer = question.answer?.includes(option);
                    const isSingleChoice = question.type === 'single_choice';
                    const inputType = isSingleChoice ? 'radio' : 'checkbox';

                    return (
                      <label
                        key={optionIndex}
                        className="flex items-start space-x-2 md:space-x-3 cursor-pointer py-1.5 md:py-3 border border-gray-100 hover:bg-gray-50 rounded-md px-2 md:px-3 mb-1.5 md:mb-2"
                      >
                        <input
                          type={inputType}
                          name={`question-${index}`}
                          value={option}
                          defaultChecked={isAnswer}
                          disabled={!isAnswer}
                          className={cn(
                            inputType === 'radio'
                              ? 'radio radio-primary radio-sm md:radio-md'
                              : 'checkbox checkbox-primary checkbox-sm md:checkbox-md',
                            'mt-0.5 md:mt-1 border-2',
                            isAnswer ? 'border-primary' : 'border-gray-300'
                          )}
                        />
                        {isHtmlContent ? (
                          <span
                            className={cn(
                              'flex-1 pt-0 md:pt-0.5 text-sm md:text-base leading-snug md:leading-relaxed',
                              isAnswer && 'font-medium text-primary'
                            )}
                            dangerouslySetInnerHTML={{ __html: option }}
                          />
                        ) : (
                          <span
                            className={cn(
                              'flex-1 pt-0 md:pt-0.5 text-sm md:text-base leading-snug md:leading-relaxed',
                              isAnswer && 'font-medium text-primary'
                            )}
                          >
                            {option}
                          </span>
                        )}
                      </label>
                    );
                  })}
                </div>
              )}

              {/* Creative Writing */}
              {question.type === 'creative_writing' && (
                <CreativeWritingRenderer
                  answer={question.answer}
                  prompt={question.prompt}
                  isHtmlContent={isHtmlContent}
                  minWords={50}
                  maxWords={300}
                />
              )}

                        {/* Explanation Accordion */}
                        {question?.explain && (
                          <ExplanationAccordion
                            explanation={question.explain}
                            isHtmlContent={isHtmlContent}
                          />
                        )}
                      </div>
                    )}
                  </Draggable>
                ))}
                {provided.placeholder}
              </div>
            )}
          </Droppable>
        </DragDropContext>
      ) : (
        /* Regular view without drag-and-drop */
        <div className="space-y-4">
          {questionsToRender?.map((question, index) => (
            <div
              key={index}
              className="mb-4 md:mb-8 p-4 md:p-6 bg-white border border-gray-200 rounded-xl shadow-lg relative"

            >


              {/* Question Header with action buttons */}
              <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 md:mb-5 pb-2 md:pb-3 border-b border-gray-200">
                <div className="flex items-center gap-2 mb-2 sm:mb-0">
                  <span className="text-lg md:text-xl font-semibold text-gray-700">
                    Question {index + 1}
                  </span>

                  {/* Compact Reorder Buttons - Inline with Question Number */}
                  {(enableArrowButtons && worksheetId && !isReadOnly) && canShowActionButtons && (
                    <div className="join">
                      <button
                        onClick={() => handleMoveUp(index)}
                        disabled={index === 0 || isLoading}
                        className="btn btn-xs btn-square btn-ghost join-item"
                        title="Move question up"
                      >
                        <ChevronUp className="w-3 h-3" />
                      </button>
                      <button
                        onClick={() => handleMoveDown(index)}
                        disabled={index === questionsToRender.length - 1 || isLoading}
                        className="btn btn-xs btn-square btn-ghost join-item"
                        title="Move question down"
                      >
                        <ChevronDown className="w-3 h-3" />
                      </button>
                    </div>
                  )}
                </div>
                <div className="flex items-center gap-2 md:gap-3">
                  {question.subject && (
                    <span className="text-xs px-2 md:px-3 py-1 bg-blue-100 text-blue-700 rounded-full font-medium">
                      {question.subject}
                    </span>
                  )}
                  <span className="text-xs px-2 md:px-3 py-1 bg-green-100 text-green-700 rounded-full font-medium capitalize">
                    {question.type.replace('_', ' ')}
                  </span>

                  {/* Streamlined Action Bar - Edit & Delete Only */}
                  {canShowActionButtons && (
                    <div className="flex items-center gap-1">
                      {/* Edit Button */}
                      <Button
                        variant="ghost"
                        onClick={() => handleEditQuestion(index)}
                        disabled={isLoading}
                        className="p-1.5 w-8 h-8 min-w-0 hover:bg-blue-50 hover:text-blue-600 rounded-md"
                        title="Edit question"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>

                      {/* Delete Button */}
                      {canShowDeleteButton && (
                        <Button
                          variant="ghost"
                          onClick={() => handleDeleteQuestion(index)}
                          disabled={deletingQuestionIndex === index}
                          className="p-1.5 w-8 h-8 min-w-0 hover:bg-red-50 hover:text-red-600 rounded-md"
                          title="Delete question"
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              </div>

              {/* Question Content */}
              <div className="bg-gray-50 p-3 md:p-5 rounded-lg mb-3 md:mb-5">
                {question.type === 'fill_blank' ? (
                  <FillBlankContentProcessor
                    content={question.content}
                    answers={question.answer}
                    isHtmlContent={isHtmlContent}
                  />
                ) : isHtmlContent ? (
                  <div
                    className="text-sm md:text-base text-gray-800 leading-relaxed question-content-html"
                    dangerouslySetInnerHTML={{ __html: question.content }}
                  />
                ) : (
                  <p className="text-sm md:text-base text-gray-800 leading-relaxed">{question.content}</p>
                )}
              </div>

              {/* SVG Illustration or Image (if available) */}
              {question?.image && (
                <div
                  className="my-4 w-full max-w-lg"
                  dangerouslySetInnerHTML={{ __html: question.image }}
                />
              )}

              {/* Question Type Specific Rendering */}
              {/* Multiple Choice & Single Choice Options */}
              {(question.type === 'multiple_choice' ||
                question.type === 'single_choice') &&
                question?.options?.length > 0 && (
                  <div className="space-y-1 mt-3 md:mt-5">
                    {question?.options?.map((option, optionIndex) => {
                      const isAnswer = question.answer?.includes(option);
                      const isSingleChoice = question.type === 'single_choice';
                      const inputType = isSingleChoice ? 'radio' : 'checkbox';

                      return (
                        <label
                          key={optionIndex}
                          className="flex items-start space-x-2 md:space-x-3 cursor-pointer py-1.5 md:py-3 border border-gray-100 hover:bg-gray-50 rounded-md px-2 md:px-3 mb-1.5 md:mb-2"
                        >
                          <input
                            type={inputType}
                            name={`question-${index}`}
                            value={option}
                            defaultChecked={isAnswer}
                            disabled={!isAnswer}
                            className={cn(
                              inputType === 'radio'
                                ? 'radio radio-primary radio-sm md:radio-md'
                                : 'checkbox checkbox-primary checkbox-sm md:checkbox-md',
                              'mt-0.5 md:mt-1 border-2',
                              isAnswer ? 'border-primary' : 'border-gray-300'
                            )}
                          />
                          {isHtmlContent ? (
                            <span
                              className={cn(
                                'flex-1 pt-0 md:pt-0.5 text-sm md:text-base leading-snug md:leading-relaxed',
                                isAnswer && 'font-medium text-primary'
                              )}
                              dangerouslySetInnerHTML={{ __html: option }}
                            />
                          ) : (
                            <span
                              className={cn(
                                'flex-1 pt-0 md:pt-0.5 text-sm md:text-base leading-snug md:leading-relaxed',
                                isAnswer && 'font-medium text-primary'
                              )}
                            >
                              {option}
                            </span>
                          )}
                        </label>
                      );
                    })}
                  </div>
                )}

              {/* Creative Writing */}
              {question.type === 'creative_writing' && (
                <CreativeWritingRenderer
                  answer={question.answer}
                  prompt={question.prompt}
                  isHtmlContent={isHtmlContent}
                  minWords={50}
                  maxWords={300}
                />
              )}

              {/* Explanation Accordion */}
              {question?.explain && (
                <ExplanationAccordion
                  explanation={question.explain}
                  isHtmlContent={isHtmlContent}
                />
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );

  return (
    <>
      {/* Render the main content */}
      {renderContent()}

      {/* Delete Question Modal */}
      <DeleteQuestionModal
        isOpen={showDeleteModal}
        onClose={cancelDeleteQuestion}
        onConfirm={confirmDeleteQuestion}
        questionNumber={(questionToDelete ?? 0) + 1}
        isLoading={deletingQuestionIndex !== null}
      />

      {/* Edit Question Modal */}
      {editingQuestion && worksheetId && (
        <QuestionFormModal
          isOpen={showEditModal}
          onClose={cancelEditQuestion}
          onSuccess={handleEditSuccess}
          worksheetId={worksheetId}
          questionTypes={[
            { value: 'MULTIPLE_CHOICE', label: 'Multiple Choice' },
            { value: 'TRUE_FALSE', label: 'True/False' },
            { value: 'FILL_IN_BLANK', label: 'Fill in the Blank' },
            { value: 'SHORT_ANSWER', label: 'Short Answer' },
            { value: 'ESSAY', label: 'Essay' },
          ]}
          mode="edit"
          initialData={{
            id: (editingQuestion as any).id || `temp-${editingQuestionIndex}`,
            type: editingQuestion.type.toUpperCase().replace(/_/g, '_') as any,
            content: editingQuestion.content,
            options: editingQuestion.options,
            answer: editingQuestion.answer,
            explain: editingQuestion.explain,
            imageUrl: editingQuestion.image || undefined,
            imagePrompt: editingQuestion.imagePrompt || undefined,
            subject: editingQuestion.subject,
            position: (editingQuestionIndex || 0) + 1,
            createdAt: new Date(),
            updatedAt: new Date(),
            version: 1,
          }}
        />
      )}
    </>
  );
};

// Explanation Accordion Component
type ExplanationAccordionProps = {
  explanation: string;
  isHtmlContent?: boolean;
};

const ExplanationAccordion: React.FC<ExplanationAccordionProps> = ({
  explanation,
  isHtmlContent = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <div className="mt-3 md:mt-5 border border-gray-200 rounded-md overflow-hidden shadow-sm">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between py-2 md:py-2.5 px-3 md:px-4 bg-gray-50 focus:outline-none transition-colors hover:bg-gray-100"
      >
        <div className="flex items-center gap-2">
          <Icon
            variant="chevron-down"
            size={3.5}
            className={cn(
              'transition-transform duration-200 text-primary',
              isOpen && 'rotate-180'
            )}
          />
          <span className="font-medium text-primary text-xs md:text-sm">
            View Explanation
          </span>
        </div>
      </button>

      <div
        className={cn(
          'transition-all duration-300 ease-in-out overflow-hidden',
          isOpen ? 'max-h-[1000px] opacity-100' : 'max-h-0 opacity-0'
        )}
      >
        <div className="p-3 md:p-4 bg-white border-t border-gray-200">
          {isHtmlContent ? (
            <div
              className="text-gray-700 text-xs md:text-sm leading-relaxed max-w-none"
              dangerouslySetInnerHTML={{
                __html: sanitizeHtml(explanation),
              }}
            />
          ) : (
            <div className="text-gray-700 text-xs md:text-sm leading-relaxed">
              {explanation.split('\n').map((paragraph, i) =>
                paragraph.trim() ? (
                  <p key={i} className="mb-2 md:mb-3">
                    {paragraph}
                  </p>
                ) : null
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default QuestionListingView;
