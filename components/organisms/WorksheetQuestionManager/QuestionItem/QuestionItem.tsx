'use client';

import React, { forwardRef } from 'react';
import { Edit, Trash2, GripVertical, Copy } from 'lucide-react';
import { IWorksheetQuestion } from '@/apis/worksheetQuestionApi';
import { Button } from '@/components/atoms/Button/Button';
import { cn } from '@/utils/cn';

export interface QuestionItemProps {
  question: IWorksheetQuestion;
  index: number;
  isSelected: boolean;
  isDragging: boolean;
  canEdit: boolean;
  canDelete: boolean;
  canReorder: boolean;
  canSelect: boolean;
  onEdit: () => void;
  onDelete: () => void;
  onSelect: (selected: boolean) => void;
  onDuplicate?: () => void;
  dragHandleProps?: any;
  isLoading: boolean;
  className?: string;
}

export const QuestionItem = forwardRef<HTMLDivElement, QuestionItemProps>(({
  question,
  index,
  isSelected,
  isDragging,
  canEdit,
  canDelete,
  canReorder,
  canSelect,
  onEdit,
  onDelete,
  onSelect,
  onDuplicate,
  dragHandleProps,
  isLoading,
  className,
  ...props
}, ref) => {
  const getQuestionTypeLabel = (type: string) => {
    const typeMap: Record<string, string> = {
      'MULTIPLE_CHOICE': 'Multiple Choice',
      'TRUE_FALSE': 'True/False',
      'FILL_IN_BLANK': 'Fill in the Blank',
      'SHORT_ANSWER': 'Short Answer',
      'ESSAY': 'Essay',
      'MATCHING': 'Matching',
      'ORDERING': 'Ordering'
    };
    return typeMap[type] || type;
  };

  const getQuestionTypeColor = (type: string) => {
    const colorMap: Record<string, string> = {
      'MULTIPLE_CHOICE': 'badge-primary',
      'TRUE_FALSE': 'badge-secondary',
      'FILL_IN_BLANK': 'badge-accent',
      'SHORT_ANSWER': 'badge-info',
      'ESSAY': 'badge-warning',
      'MATCHING': 'badge-success',
      'ORDERING': 'badge-error'
    };
    return colorMap[type] || 'badge-neutral';
  };

  const getDifficultyColor = (difficulty?: string) => {
    const colorMap: Record<string, string> = {
      'EASY': 'text-success',
      'MEDIUM': 'text-warning',
      'HARD': 'text-error'
    };
    return difficulty ? colorMap[difficulty] || 'text-base-content' : 'text-base-content';
  };

  return (
    <div
      ref={ref}
      {...props}
      className={cn(
        "card bg-base-100 border border-base-300 shadow-sm transition-all duration-200",
        isDragging && "shadow-lg rotate-2 scale-105",
        isSelected && "ring-2 ring-primary ring-opacity-50",
        "hover:shadow-md",
        className
      )}
    >
      <div className="card-body p-4">
        {/* Question Header */}
        <div className="flex items-start justify-between gap-4">
          <div className="flex items-center gap-3 flex-1 min-w-0">
            {/* Drag Handle */}
            {canReorder && (
              <div
                {...dragHandleProps}
                className="cursor-grab active:cursor-grabbing text-base-content/50 hover:text-base-content transition-colors"
                aria-label="Drag to reorder"
              >
                <GripVertical className="w-5 h-5" />
              </div>
            )}

            {/* Selection Checkbox */}
            {canSelect && (
              <input
                type="checkbox"
                className="checkbox checkbox-primary"
                checked={isSelected}
                onChange={(e) => onSelect(e.target.checked)}
                disabled={isLoading}
                aria-label={`Select question ${index + 1}`}
              />
            )}

            {/* Question Number and Type */}
            <div className="flex items-center gap-2">
              <span className="font-medium text-base-content">
                Q{index + 1}
              </span>
              <div className={cn("badge badge-sm", getQuestionTypeColor(question.type))}>
                {getQuestionTypeLabel(question.type)}
              </div>
              {question.difficulty && (
                <span className={cn("text-xs font-medium", getDifficultyColor(question.difficulty))}>
                  {question.difficulty}
                </span>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-1">
            {onDuplicate && (
              <Button
                variant="ghost"
                size="sm"
                onClick={onDuplicate}
                disabled={isLoading}
                className="btn-ghost btn-sm"
                aria-label="Duplicate question"
              >
                <Copy className="w-4 h-4" />
              </Button>
            )}

            {canEdit && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log('Edit button clicked for question:', question.id);
                  onEdit();
                }}
                disabled={isLoading}
                className="btn-ghost btn-sm hover:bg-blue-50 hover:text-blue-600"
                aria-label="Edit question"
              >
                <Edit className="w-4 h-4" />
              </Button>
            )}

            {canDelete && (
              <Button
                variant="ghost"
                size="sm"
                onClick={(e) => {
                  e.preventDefault();
                  e.stopPropagation();
                  console.log('Delete button clicked for question:', question.id);
                  onDelete();
                }}
                disabled={isLoading}
                className="btn-ghost btn-sm text-error hover:bg-error/10 hover:text-error"
                aria-label="Delete question"
              >
                <Trash2 className="w-4 h-4" />
              </Button>
            )}
          </div>
        </div>

        {/* Question Content */}
        <div className="mt-3">
          <div className="prose prose-sm max-w-none">
            <p className="text-base-content mb-2 line-clamp-3">
              {question.content}
            </p>
          </div>

          {/* Question Options Preview */}
          {question.options && question.options.length > 0 && (
            <div className="mt-2">
              <div className="text-xs text-base-content/70 mb-1">Options:</div>
              <div className="flex flex-wrap gap-1">
                {question.options.slice(0, 3).map((option, idx) => (
                  <span
                    key={idx}
                    className={cn(
                      "badge badge-outline badge-sm",
                      question.answer?.includes(option) && "badge-success"
                    )}
                  >
                    {option.length > 20 ? `${option.substring(0, 20)}...` : option}
                  </span>
                ))}
                {question.options.length > 3 && (
                  <span className="badge badge-ghost badge-sm">
                    +{question.options.length - 3} more
                  </span>
                )}
              </div>
            </div>
          )}

          {/* Question Metadata */}
          <div className="flex items-center justify-between mt-3 text-xs text-base-content/70">
            <div className="flex items-center gap-4">
              {question.points && (
                <span>{question.points} point{question.points !== 1 ? 's' : ''}</span>
              )}
              {question.subject && (
                <span>{question.subject}</span>
              )}
              {question.grade && (
                <span>{question.grade}</span>
              )}
            </div>
            {question.position && (
              <span>Position: {question.position}</span>
            )}
          </div>
        </div>
      </div>
    </div>
  );
});
